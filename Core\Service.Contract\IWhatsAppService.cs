using Core.Entities;

namespace Core.Service.Contract
{
    public interface IWhatsAppService
    {
        // Chat management
        Task<IEnumerable<Chat>> GetAllChatsAsync();
        Task<IEnumerable<Chat>> GetChatsByAssignedUserAsync(string userId);
        Task<IEnumerable<Chat>> SearchChatsAsync(string searchTerm);
        Task<Chat?> GetChatByIdAsync(int chatId, bool includeMessages = true);
        Task<Chat?> GetChatByPhoneAsync(string phoneNumber, bool includeMessages = true);
        Task<Chat> CreateOrUpdateChatAsync(string customerName, string customerPhone, string? customerEmail = null);
        
        Task<bool> AssignChatToUserAsync(int chatId, string userId);
        Task<bool> ArchiveChatAsync(int chatId);
        Task<bool> UnarchiveChatAsync(int chatId);
        Task<bool> DeleteChatAsync(int chatId);

        // Message management
        Task<IEnumerable<Message>> GetMessagesByChatIdAsync(int chatId, int skip = 0, int take = 50);
        Task<IEnumerable<Message>> GetMessagesByChatIdFromDateAsync(int chatId, DateTime fromDate);
        Task<IEnumerable<Message>> GetUnreadMessagesAsync();
        Task<IEnumerable<Message>> GetUnreadMessagesByChatIdAsync(int chatId);
        Task<IEnumerable<Message>> GetUnreadMessagesByUserAsync(string userId);
        Task<Message> SendMessageAsync(int chatId, string content, string? sentByUserId = null, string messageType = "text");
        Task<Message> ReceiveMessageAsync(int chatId, string content, string messageType = "text");
        Task<bool> MarkMessageAsReadAsync(int messageId);
        Task<bool> MarkMessagesAsReadAsync(int chatId);
        Task<bool> DeleteMessageAsync(int messageId);

        // Auto reply management
        Task<string?> ProcessAutoReplyAsync(string messageContent);
        Task<bool> SendAutoReplyAsync(int chatId, string messageContent);

        // Statistics
        Task<int> GetTotalChatsCountAsync();
        Task<int> GetActiveChatsCountAsync();
        Task<int> GetPendingChatsCountAsync();
        Task<int> GetInactiveChatsCountAsync();
        Task<int> GetUnreadMessagesCountAsync();
        Task<int> GetUnreadMessagesCountByUserAsync(string userId);
    }
}
