﻿using Core.Service.Contract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Text.Json;


[ApiController]
[Route("webhook")]
public class WebhookController : ControllerBase
{
    
    private readonly IWhatsAppCloudeService _whatsAppService;

    public WebhookController( IWhatsAppCloudeService whatsAppService)
    {
        
        _whatsAppService = whatsAppService;
    }

    // STEP 1: Verification
    [HttpGet]
    public IActionResult Verify([FromQuery(Name = "hub.mode")] string hubMode,
                                [FromQuery(Name = "hub.challenge")] string hubChallenge,
                                [FromQuery(Name = "hub.verify_token")] string hubVerifyToken)
    {
        const string VERIFY_TOKEN = "myverifytoken"; // use the same one you set in Meta portal

        if (hubMode == "subscribe" && hubVerifyToken == VERIFY_TOKEN)
        {
            return Ok(hubChallenge);
        }

        return Unauthorized();
    }

  

    [HttpPost()]
    [AllowAnonymous]
    public async Task<IActionResult> Receive( JsonElement payload)
    {
        try
        {
            // Make sure the payload has the expected structure
            var entry = payload.GetProperty("entry")[0];
            var changes = entry.GetProperty("changes")[0];
            var value = changes.GetProperty("value");

            // Sometimes "messages" might not exist (like status updates)
            if (value.TryGetProperty("messages", out JsonElement messages))
            {
                var messageObj = messages[0];

                // Get the message text
                string? messageText = messageObj.GetProperty("text")
                                               .GetProperty("body")
                                               .GetString();

                // Get the user phone number (who sent the message)
                string? fromNumber = messageObj.GetProperty("from").GetString();
                if (string.IsNullOrEmpty(messageText) || string.IsNullOrEmpty(fromNumber))
                {
                    Console.WriteLine("Message text or from number is null/empty");
                    return Ok();
                }

                string? name = null;

                name = value.GetProperty("contacts")[0]
                      .GetProperty("profile")
                      .GetProperty("name")
                      .GetString();




                Console.WriteLine($"Message from {name} - {fromNumber}: {messageText}");
                await _whatsAppService.RecieveMessage(fromNumber, messageText , name);


                // var result = await _whatsAppService.SendTextMessage(fromNumber);

            }

            return Ok();
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error reading payload: " + ex.Message);
            return BadRequest();
        }
    }

}
