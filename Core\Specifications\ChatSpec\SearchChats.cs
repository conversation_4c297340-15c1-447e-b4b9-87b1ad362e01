using Core.Entities;

namespace Core.Specifications.ChatSpec
{
    public class SearchChats : Specification<Chat>
    {
        public SearchChats(string searchTerm) : base(c => 
            c.Customer.Name.Contains(searchTerm) || 
            c.Customer.Number.Contains(searchTerm) )
                  {
            includes.Add(c => c.Customer);
            includes.Add(c => c.Messages.OrderByDescending(m => m.CreatedAt).Take(1));
            includes.Add(c => c.AssignedUser);
            OrderByDesc = c => c.LastMessageAt;
        }

    }
}
