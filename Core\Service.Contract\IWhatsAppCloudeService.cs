﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Service.Contract
{
    public interface IWhatsAppCloudeService
    {
        //Task<bool> SendMessage(string mobile, string language, string template, List<WhatsAppComponent>? components = null);
        Task<bool> SendTextMessage(string mobile, string message);
        Task  RecieveMessage(string mobile, string message, string name);
    }
}
