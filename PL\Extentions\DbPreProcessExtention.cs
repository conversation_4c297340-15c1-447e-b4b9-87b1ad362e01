﻿using Core.Entities;
using DAL.Data.Seeding;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Repository.Data;
using Repository.Data.DataSeeding;

namespace Template.Extentions
{
    public static class DbPreProcessExtention
    {
        public static async void DbPreProcess(this WebApplication app)
        {
            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var dbContext = services.GetRequiredService<AppDbContext>();

            var userManager = services.GetRequiredService<UserManager<User>>();
            var loggerFactory = services.GetRequiredService<ILoggerFactory>();
            try
            {
                await dbContext.Database.MigrateAsync();

                Seeding.SeedingHelper(dbContext);
                await IdentitySeeder.SeedRolesAndAdmin(userManager, services.GetRequiredService<RoleManager<IdentityRole>>());

            }
            catch (Exception ex)
            {
                var logger = loggerFactory.CreateLogger<Program>();
                logger.LogError(ex.Message);

            }

        }

    }
}
