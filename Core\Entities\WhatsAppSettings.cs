using System.ComponentModel.DataAnnotations;

namespace Core.Entities
{
    public class WhatsAppSettings : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string SettingKey { get; set; } = string.Empty;

        [Required]
        [<PERSON><PERSON>eng<PERSON>(2000)]
        public string SettingValue { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(20)]
        public string DataType { get; set; } = "string"; // string, boolean, number, json

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Updated by user
        public string? UpdatedByUserId { get; set; }
        public virtual User? UpdatedByUser { get; set; }
    }
}
