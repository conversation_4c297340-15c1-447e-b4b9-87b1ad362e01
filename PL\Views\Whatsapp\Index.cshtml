@{
    ViewData["Title"] = "WhatsApp Chat";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@Html.AntiForgeryToken()

<style>
    .whatsapp-container {
        height: calc(100vh - 120px);
        background: #f0f2f5;
        display: flex;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .chat-sidebar {
        width: 350px;
        background: white;
        border-right: 1px solid #e9ecef;
        display: flex;
        flex-direction: column;
    }

    .sidebar-header {
        padding: 20px;
        background: #00a884;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .sidebar-header h5 {
        margin: 0;
        font-weight: 600;
    }

    .search-box {
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
    }

    .search-box input {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
    }

    .chat-list {
        flex: 1;
        overflow-y: auto;
    }

    .chat-item {
        padding: 15px 20px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;
        display: flex;
        align-items: center;
    }

    .chat-item:hover {
        background: #f5f5f5;
    }

    .chat-item.active {
        background: #e3f2fd;
        border-right: 3px solid #00a884;
    }

    .chat-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #00a884;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-left: 15px;
        font-size: 18px;
    }

    .chat-info {
        flex: 1;
    }

    .chat-name {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .chat-last-message {
        color: #666;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chat-time {
        font-size: 12px;
        color: #999;
        margin-bottom: 5px;
    }

    .unread-count {
        background: #00a884;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
    }

    .assign-user-btn {
        background: none;
        border: none;
        color: #666;
        font-size: 16px;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: background-color 0.2s;
        position: relative;
    }

    .chat-actions-container {
        position: relative;
        display: inline-block;
    }

    .assign-user-btn:hover {
        background: #f0f0f0;
        color: #00a884;
    }

    .user-dropdown {
        position: absolute;
        top: 100%;
        left: -180px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        min-width: 200px;
        z-index: 1000;
        display: none;
        max-height: 200px;
        overflow-y: auto;
    }

    .user-dropdown.show {
        display: block;
    }

    .user-dropdown-item {
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s;
    }

    .user-dropdown-item:hover {
        background: #f5f5f5;
    }

    .user-dropdown-item:last-child {
        border-bottom: none;
    }

    .user-dropdown-item .user-name {
        font-weight: 600;
        color: #333;
    }

    .user-dropdown-item .user-email {
        font-size: 12px;
        color: #666;
    }

    .assigned-user-badge {
        background: #e3f2fd;
        color: #1976d2;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        margin-top: 2px;
        display: inline-block;
    }

    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #e5ddd5;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><defs><pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
    }

    .chat-header {
        background: white;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .chat-header-info {
        display: flex;
        align-items: center;
    }

    .chat-header-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #00a884;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-left: 15px;
    }

    .chat-header-details h6 {
        margin: 0;
        font-weight: 600;
        color: #333;
    }

    .chat-header-details small {
        color: #666;
    }

    .chat-actions {
        display: flex;
        gap: 10px;
    }

    .chat-actions button {
        background: none;
        border: none;
        color: #666;
        font-size: 18px;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: background-color 0.2s;
    }

    .chat-actions button:hover {
        background: #f0f0f0;
    }

    .chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .message {
        max-width: 70%;
        padding: 10px 15px;
        border-radius: 15px;
        position: relative;
        word-wrap: break-word;
    }

    .message.sent {
        background: #dcf8c6;
        align-self: flex-end;
        margin-right: 10px;
    }

    .message.received {
        background: white;
        align-self: flex-start;
        margin-left: 10px;
    }

    .message-time {
        font-size: 11px;
        color: #666;
        margin-top: 5px;
        text-align: left;
    }

    .message.sent .message-time {
        text-align: right;
    }

    .chat-input {
        background: white;
        padding: 15px 20px;
        border-top: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .chat-input input {
        flex: 1;
        padding: 12px 20px;
        border: 1px solid #ddd;
        border-radius: 25px;
        outline: none;
        font-size: 14px;
    }

    .chat-input button {
        background: #00a884;
        color: white;
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .chat-input button:hover {
        background: #008f6f;
    }

    .empty-chat {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;
        text-align: center;
    }

    .empty-chat i {
        font-size: 80px;
        margin-bottom: 20px;
        color: #ddd;
    }

 /*    media (max-width: 768px) {
        .whatsapp-container {
            height: calc(100vh - 80px);
        }
        
        .chat-sidebar {
            width: 100%;
            position: absolute;
            z-index: 1000;
            height: 100%;
        }
        
        .chat-main {
            width: 100%;
        }
        
        .chat-sidebar.hidden {
            display: none;
        }
    } */
</style>




<section class="content">
    <div class="container-fluid">
        <div class="whatsapp-container">
            <!-- Chat Sidebar -->
            <div class="chat-sidebar" id="chatSidebar">
                <div class="sidebar-header">
                    <h5>المحادثات</h5>
                    <div>
                        <button class="btn btn-sm btn-light" onclick="showServiceCustomers()">
                            <i class="fas fa-users"></i>
                        </button>
                        <button class="btn btn-sm btn-light" onclick="showSettings()">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                
                <div class="search-box">
                    <input type="text" placeholder="البحث في المحادثات..." id="searchChats">
                </div>
                
                <div class="chat-list" id="chatList">
                    <!-- Chat items will be populated here -->
                </div>
            </div>

            <!-- Chat Main Area -->
            <div class="chat-main" id="chatMain">
                <div class="empty-chat">
                    <i class="fab fa-whatsapp"></i>
                    <h4>مرحباً بك في WhatsApp</h4>
                    <p>اختر محادثة لبدء المراسلة</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Global variables
    let chats = [];
    let currentChatId = null;
    let currentChatMessages = [];
    let systemUsers = [];

    // Load chats from API
    async function loadChats() {
        try {
            showLoading('chatList');

            // Load chats and users in parallel
            const [chatsResponse, usersResponse] = await Promise.all([
                fetch('/Whatsapp/GetChats'),
                fetch('/Whatsapp/GetSystemUsers')
            ]);

            const chatsData = await chatsResponse.json();
            const usersData = await usersResponse.json();

            if (chatsData.success) {
                chats = chatsData.chats;
                renderChatList();
            } else {
                showError('خطأ في تحميل المحادثات: ' + chatsData.message);
            }

            if (usersData.success) {
                systemUsers = usersData.users;
            } else {
                console.error('Error loading users:', usersData.message);
            }
        } catch (error) {
            console.error('Error loading chats:', error);
            showError('خطأ في الاتصال بالخادم');
        }
    }

    // Render chat list
    function renderChatList() {
        const chatList = document.getElementById('chatList');
        chatList.innerHTML = '';

        if (chats.length === 0) {
            chatList.innerHTML = '<div class="text-center p-3 text-muted">لا توجد محادثات</div>';
            return;
        }

        chats.forEach(chat => {
            const chatItem = document.createElement('div');
            chatItem.className = 'chat-item';
            chatItem.onclick = (e) => {
                // Don't open chat if clicking on assign button
                if (!e.target.closest('.assign-user-btn')) {
                    openChat(chat.id);
                }
            };

            const assignedUserBadge = chat.assignedUser ?
                `<div class="assigned-user-badge">${chat.assignedUser.fullName}</div>` : '';

            chatItem.innerHTML = `
                <div class="chat-avatar">${chat.customerName[0].toUpperCase()}</div>
                <div class="chat-info">
                    <div class="chat-name">${chat.customerName}</div>
                    <div class="chat-last-message">${chat.lastMessage || 'لا توجد رسائل'}</div>
                    ${assignedUserBadge}
                </div>
                <div style="text-align: center;">
                    <div class="chat-time">${chat.lastMessageAt}</div>
                    ${chat.unreadCount > 0 ? `<div class="unread-count">${chat.unreadCount}</div>` : ''}
                    <div class="chat-actions-container">
                        <button class="assign-user-btn" onclick="toggleUserDropdown(event, ${chat.id})" title="تعيين مستخدم">
                            <i class="fas fa-user-plus"></i>
                        </button>
                        <div class="user-dropdown" id="dropdown-${chat.id}">
                            ${renderUserDropdown()}
                        </div>
                    </div>
                </div>
            `;

            chatList.appendChild(chatItem);
        });
    }

    // Open chat
    async function openChat(chatId) {
        try {
            currentChatId = chatId;
            const chat = chats.find(c => c.id === chatId);

            if (!chat) return;

            // Update active chat in sidebar
            document.querySelectorAll('.chat-item').forEach(item => item.classList.remove('active'));
            event.currentTarget.classList.add('active');

            // Show loading in chat area
            const chatMain = document.getElementById('chatMain');
            chatMain.innerHTML = `
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="chat-header-avatar">${chat.customerName[0].toUpperCase()}</div>
                        <div class="chat-header-details">
                            <h6>${chat.customerName}</h6>
                            <small>متصل الآن</small>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button><i class="fas fa-phone"></i></button>
                        <button><i class="fas fa-video"></i></button>
                        <button><i class="fas fa-ellipsis-v"></i></button>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="text-center p-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p>جاري تحميل الرسائل...</p>
                    </div>
                </div>

                <div class="chat-input">
                    <button><i class="fas fa-paperclip"></i></button>
                    <input type="text" placeholder="اكتب رسالة..." id="messageInput" onkeypress="handleKeyPress(event)">
                    <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
                </div>
            `;

            // Load messages from API
            await loadChatMessages(chatId);

            // Mark messages as read
            await markMessagesAsRead(chatId);

        } catch (error) {
            console.error('Error opening chat:', error);
            showError('خطأ في فتح المحادثة');
        }
    }

    // Load chat messages from API
    async function loadChatMessages(chatId) {
        try {
            const response = await fetch(`/Whatsapp/GetChatMessages?chatId=${chatId}`);
            const data = await response.json();

            if (data.success) {
                currentChatMessages = data.messages;
                renderChatMessages();
            } else {
                showError('خطأ في تحميل الرسائل: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading messages:', error);
            showError('خطأ في تحميل الرسائل');
        }
    }

    // Render chat messages
    function renderChatMessages() {
        const messagesContainer = document.getElementById('chatMessages');

        if (currentChatMessages.length === 0) {
            messagesContainer.innerHTML = '<div class="text-center p-3 text-muted">لا توجد رسائل</div>';
            return;
        }

        messagesContainer.innerHTML = currentChatMessages.map(msg => `
            <div class="message ${msg.isSent ? 'sent' : 'received'}">
                ${msg.content}
                <div class="message-time">${msg.createdAt}</div>
                ${msg.isAutoReply ? '<small class="text-muted">رد تلقائي</small>' : ''}
            </div>
        `).join('');

        // Scroll to bottom
        setTimeout(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 100);
    }

    // Send message
    async function sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();

        if (!message || !currentChatId) return;

        try {
            // Disable input while sending
            input.disabled = true;

            // Add message to UI immediately (optimistic update)
            const tempMessage = {
                content: message,
                isSent: true,
                createdAt: new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' }),
                isAutoReply: false
            };
            currentChatMessages.push(tempMessage);
            renderChatMessages();

            // Clear input
            input.value = '';

            // Send to API
            const formData = new FormData();
            formData.append('chatId', currentChatId);
            formData.append('message', message);

            const response = await fetch('/Whatsapp/SendMessage', {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                // Reload messages to get the actual message with ID
                await loadChatMessages(currentChatId);
                // Reload chat list to update last message
                await loadChats();
            } else {
                // Remove optimistic message on error
                currentChatMessages.pop();
                renderChatMessages();
                showError('خطأ في إرسال الرسالة: ' + data.message);
            }

        } catch (error) {
            console.error('Error sending message:', error);
            // Remove optimistic message on error
            currentChatMessages.pop();
            renderChatMessages();
            showError('خطأ في إرسال الرسالة');
        } finally {
            input.disabled = false;
            input.focus();
        }
    }

    // Handle enter key
    function handleKeyPress(event) {
        if (event.key === 'Enter') {
            sendMessage();
        }
    }

    // Mark messages as read
    async function markMessagesAsRead(chatId) {
        try {
            const formData = new FormData();
            formData.append('chatId', chatId);

            await fetch('/Whatsapp/MarkMessagesAsRead', {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            // Update chat list to reflect read status
            await loadChats();
        } catch (error) {
            console.error('Error marking messages as read:', error);
        }
    }

    // Search chats
    async function searchChats(searchTerm) {
        if (!searchTerm.trim()) {
            await loadChats();
            return;
        }

        try {
            const response = await fetch(`/Whatsapp/SearchChats?searchTerm=${encodeURIComponent(searchTerm)}`);
            const data = await response.json();

            if (data.success) {
                chats = data.chats;
                renderChatList();
            } else {
                showError('خطأ في البحث: ' + data.message);
            }
        } catch (error) {
            console.error('Error searching chats:', error);
            showError('خطأ في البحث');
        }
    }

    // Search input handler
    let searchTimeout;
    document.getElementById('searchChats').addEventListener('input', function(e) {
        const searchTerm = e.target.value;

        // Debounce search
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            searchChats(searchTerm);
        }, 300);
    });

    // Show service customers (will be implemented in separate view)
    function showServiceCustomers() {
        window.location.href = '@Url.Action("ServiceCustomers", "Whatsapp")';
    }

    // Show settings (will be implemented in separate view)
    function showSettings() {
        window.location.href = '@Url.Action("Settings", "Whatsapp")';
    }

    // User assignment functions
    function renderUserDropdown() {
        if (!systemUsers || systemUsers.length === 0) {
            return '<div class="user-dropdown-item">لا توجد مستخدمين</div>';
        }

        return systemUsers.map(user => `
            <div class="user-dropdown-item" onclick="assignUserToChat(event, ${currentDropdownChatId}, '${user.id}')">
                <div class="user-name">${user.fullName}</div>
                <div class="user-email">${user.email}</div>
            </div>
        `).join('');
    }

    let currentDropdownChatId = null;

    function toggleUserDropdown(event, chatId) {
        event.stopPropagation();

        // Close all other dropdowns
        document.querySelectorAll('.user-dropdown').forEach(dropdown => {
            if (dropdown.id !== `dropdown-${chatId}`) {
                dropdown.classList.remove('show');
            }
        });

        const dropdown = document.getElementById(`dropdown-${chatId}`);
        currentDropdownChatId = chatId;

        // Update dropdown content with current users
        dropdown.innerHTML = renderUserDropdown();

        dropdown.classList.toggle('show');
    }

    async function assignUserToChat(event, chatId, userId) {
        event.stopPropagation();

        try {
            const formData = new FormData();
            formData.append('chatId', chatId);
            formData.append('userId', userId);

            const response = await fetch('/Whatsapp/AssignUser', {
                method: 'POST',
                body: formData,
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const data = await response.json();

            if (data.success) {
                // Close dropdown
                document.getElementById(`dropdown-${chatId}`).classList.remove('show');

                // Reload chats to reflect the assignment
                await loadChats();

                showSuccess('تم تعيين المستخدم بنجاح');
            } else {
                showError('خطأ في تعيين المستخدم: ' + data.message);
            }
        } catch (error) {
            console.error('Error assigning user:', error);
            showError('خطأ في تعيين المستخدم');
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.assign-user-btn') && !event.target.closest('.user-dropdown')) {
            document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });

    // Utility functions
    function showLoading(elementId) {
        const element = document.getElementById(elementId);
        element.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p>جاري التحميل...</p>
            </div>
        `;
    }

    function showError(message) {
        // You can implement a toast notification or alert here
        alert(message);
    }

    function showSuccess(message) {
        // You can implement a toast notification or alert here
        alert(message);
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadChats();
    });
</script>
