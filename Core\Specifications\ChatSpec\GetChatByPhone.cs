using Core.Entities;

namespace Core.Specifications.ChatSpec
{
    public class GetChatByPhone : Specification<Chat>
    {
        public GetChatByPhone(string phoneNumber) : base(c => c.Customer.Number == phoneNumber)
        {
            includes.Add(c => c.Messages.OrderBy(m => m.CreatedAt));
            includes.Add(c => c.AssignedUser);
        }

        public GetChatByPhone(string phoneNumber, bool includeMessages) : base(c => c.Customer.Number == phoneNumber)
        {
            if (includeMessages)
            {
                includes.Add(c => c.Messages.OrderBy(m => m.CreatedAt));
            }
            includes.Add(c => c.AssignedUser);
        }
    }
}
