/* تعزيزات إبداعية بطابع عربي */
body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    direction: rtl;
}

.dashboard-title {
    font-family: '<PERSON><PERSON>', serif;
    font-size: 2.4rem;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

    .dashboard-title::after {
        content: "";
        display: block;
        width: 100px;
        height: 3px;
        background: linear-gradient(to left, #4CAF50, #2196F3);
        margin: 10px auto;
        border-radius: 5px;
    }

.islamic-pattern {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path fill="none" stroke="rgba(0,0,0,0.07)" stroke-width="1" d="M50 0 L100 50 L50 100 L0 50 Z M50 25 L75 50 L50 75 L25 50 Z"></path></svg>');
    background-size: 100px 100px;
}

.stat-card {
    border-radius: 15px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
}

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }

.gradient-info {
    background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.gradient-dark {
    background: linear-gradient(135deg, #2C3E50, #4CA1AF);
}

.action-btn {
    transition: all 0.3s ease;
    border-radius: 50px;
    margin: 5px;
    border-width: 2px !important;
}

    .action-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

.card-header {
    background-color: #f8f9fa;
    border-bottom: 3px solid #e9ecef;
    padding: 15px;
}

.card-outline {
    border-top: 3px solid #4CAF50;
    transition: all 0.3s ease;
}

    .card-outline:hover {
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

.arabic-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><path fill="none" stroke="rgba(0,128,0,0.05)" stroke-width="1" d="M0,50 Q50,0 100,50 Q150,100 200,50 Q150,100 100,150 Q50,200 0,150 Q50,100 0,50 Z"></path></svg>');
    background-size: 100px 100px;
    opacity: 0.1;
    z-index: -1;
}

.table-title {
    position: relative;
    display: inline-block;
    padding-right: 15px;
}

    .table-title:before {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 5px;
        height: 20px;
        background-color: #4CAF50;
        border-radius: 3px;
    }

.table-hover tbody tr:hover {
    background-color: rgba(76, 175, 80, 0.05);
}

.btn-tool {
    color: #6c757d;
    background-color: transparent;
    border: none;
    padding: 0.25rem 0.5rem;
    transition: all 0.2s ease;
}

    .btn-tool:hover {
        color: #4CAF50;
        transform: rotate(15deg);
    }

.view-all-link {
    display: inline-block;
    margin-top: 15px;
    color: #4CAF50;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

    .view-all-link:hover {
        color: #2C3E50;
        transform: translateX(-5px);
    }

    .view-all-link i {
        margin-right: 5px;
        transition: all 0.3s ease;
    }

    .view-all-link:hover i {
        transform: translateX(-3px);
    }

/* إضافة تأثير للجداول */
.table-container {
    position: relative;
    overflow: hidden;
}

    .table-container::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 30px;
        background: linear-gradient(to top, rgba(255,255,255,1), rgba(255,255,255,0));
        pointer-events: none;
        opacity: 0.8;
    }

/* تحسينات جديدة للتمرير الأفقي للجداول */
.scrollable-table-wrapper {
    overflow-x: auto;
    max-width: 100%;
    margin-bottom: 15px;
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
}

/* مؤشرات التمرير */
.scrollable-hint {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(76, 175, 80, 0.5);
    font-size: 20px;
    z-index: 5;
    pointer-events: none;
    animation: pulse 2s infinite;
    display: none;
}

    .scrollable-hint.right {
        left: 10px;
    }

    .scrollable-hint.left {
        right: 10px;
    }

.scrollable-table-wrapper:hover .scrollable-hint {
    display: block;
}

@keyframes pulse {
    0% {
        opacity: 0.3;
    }

    50% {
        opacity: 0.8;
    }

    100% {
        opacity: 0.3;
    }
}

/* تثبيت رؤوس الجداول */
.sticky-header th {
    position: sticky;
    top: 0;
    background-color: #f0f0f0;
    z-index: 2;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

/* تعزيز عرض الجدول لوضع RTL */
.table-wrapper-rtl {
    direction: rtl;
}

    .table-wrapper-rtl table {
        width: auto !important;
        min-width: 100%;
    }

/* شريط التمرير المخصص */
.scrollable-table-wrapper::-webkit-scrollbar {
    height: 8px;
}

.scrollable-table-wrapper::-webkit-scrollbar-thumb {
    background: #4CAF50;
    border-radius: 10px;
}

.scrollable-table-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

/* أنماط جديدة للإحصاءات */
.stats-card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: none;
    position: relative;
    z-index: 1;
    height: 100%;
}

    .stats-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stats-card:hover::before {
        opacity: 1;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 14px 28px rgba(0,0,0,0.15), 0 10px 10px rgba(0,0,0,0.12);
    }

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
    margin-bottom: 15px;
}

.stats-value {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

.stats-card-primary {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
}

.stats-card-success {
    background: linear-gradient(135deg, #1cc88a, #13855c);
    color: white;
}

.stats-card-danger {
    background: linear-gradient(135deg, #e74a3b, #be2617);
    color: white;
}

.stats-card-warning {
    background: linear-gradient(135deg, #f6c23e, #dda20a);
    color: white;
}

/* أنماط البطاقات المدمجة */
.stats-card {
    border-radius: 10px !important;
}

.small-icon {
    font-size: 1.8rem !important;
    margin-bottom: 10px !important;
}

.small-value {
    font-size: 1.8rem !important;
    margin-bottom: 3px !important;
}

.small-label {
    font-size: 1rem !important;
}

.card-body {
    padding: 1rem !important;
}
