using Core.Entities;

namespace Core.Specifications.WhatsAppSettingsSpec
{
    public class GetAllSettings : Specification<WhatsAppSettings>
    {
        public GetAllSettings() : base(s => s.IsActive)
        {
            includes.Add(s => s.UpdatedByUser);
            OrderBy = s => s.Set<PERSON>;
        }

        public GetAllSettings(bool includeInactive) : base(s => includeInactive || s.IsActive)
        {
            includes.Add(s => s.UpdatedByUser);
            OrderBy = s => s.<PERSON>ting<PERSON>ey;
        }

        public GetAllSettings(string dataType) : base(s => s.DataType == dataType && s.IsActive)
        {
            includes.Add(s => s.UpdatedByUser);
            OrderBy = s => s.<PERSON>;
        }
    }
}
