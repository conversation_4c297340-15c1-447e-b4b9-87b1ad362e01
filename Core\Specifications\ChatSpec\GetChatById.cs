using Core.Entities;

namespace Core.Specifications.ChatSpec
{
    public class GetChatById : Specification<Chat>
    {
        public GetChatById(int chatId) : base(c => c.Id == chatId)
        {
            includes.Add(c => c.Customer);
            includes.Add(c => c.Messages.OrderBy(m => m.CreatedAt));
            includes.Add(c => c.AssignedUser);
        }

        public GetChatById(int chatId, bool includeMessages) : base(c => c.Id == chatId)
        {
            if (includeMessages)
            {
                includes.Add(c => c.Messages.OrderBy(m => m.CreatedAt));
            }
            includes.Add(c => c.AssignedUser);
        }
    }
}
