using Core.Entities;

namespace Core.Specifications.AutoReplySpec
{
    public class FindMatchingAutoReply : Specification<AutoReply>
    {
        public FindMatchingAutoReply(string messageContent) : base(ar => 
            ar.IsActive && 
            ( messageContent.ToLower().Contains(ar.Trigger.ToLower()) ||
           messageContent.ToLower() == ar.Trigger.ToLower() ||
            messageContent.ToLower().StartsWith(ar.Trigger.ToLower()) ||
              messageContent.ToLower().EndsWith(ar.Trigger.ToLower())))
        {
            includes.Add(ar => ar.CreatedByUser);
            OrderByDesc = ar => ar.Priority;
        }

       
    }
}
