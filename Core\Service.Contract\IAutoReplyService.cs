using Core.Entities;

namespace Core.Service.Contract
{
    public interface IAutoReplyService
    {
        // Auto reply management
        Task<IEnumerable<AutoReply>> GetAllAutoRepliesAsync();
        Task<IEnumerable<AutoReply>> GetActiveAutoRepliesAsync();
        Task<IEnumerable<AutoReply>> GetAutoRepliesByUserAsync(string userId);
        Task<AutoReply?> GetAutoReplyByIdAsync(int id);
        Task<AutoReply?> FindMatchingAutoReplyAsync(string messageContent);
        Task<AutoReply> CreateAutoReplyAsync(string trigger, string response, string? createdByUserId = null, 
            string matchType = "contains", bool isCaseSensitive = false, int priority = 1);
        Task<bool> UpdateAutoReplyAsync(int id, string trigger, string response, string matchType = "contains", 
            bool isCaseSensitive = false, int priority = 1);
        Task<bool> ToggleAutoReplyStatusAsync(int id);
        Task<bool> DeleteAutoReplyAsync(int id);
        Task<bool> UpdateAutoReplyUsageAsync(int id);

        // Auto reply processing
        Task<string?> ProcessMessageForAutoReplyAsync(string messageContent);
        Task<bool> IsAutoReplyEnabledAsync();
    }
}
