@model User
@{
    ViewData["Title"] = "تعديل مستخدم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">

<!-- Custom Styles -->
<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .form-label {
        font-weight: bold;
    }

    .form-control {
        border-radius: 8px;
    }

    .btn:hover {
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }

    .required::after {
        content: " *";
        color: red;
    }
</style>

<!-- Page Content -->
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-body">
            <h2 class="text-primary mb-4">
                <i class="bi bi-pencil-square me-2"></i> تعديل مستخدم
            </h2>

            <form asp-action="Edit" method="post">
                <input type="hidden" asp-for="Id" />

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="FullName" class="form-label required"><i class="bi bi-person-circle me-1"></i> الاسم الكامل</label>
                        <input asp-for="FullName" class="form-control" required />

                    </div>

                    <div class="col-md-6 mb-3">
                        <label asp-for="Email" class="form-label required"><i class="bi bi-envelope me-1"></i> البريد الإلكتروني</label>
                        <input asp-for="Email" class="form-control" type="email" required />

                    </div>
                </div>


                <div class="d-flex justify-content-between mt-4">
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-save"></i> تحديث المستخدم
                    </button>
                    <a class="btn btn-secondary" asp-action="DepartmentUsers">
                        <i class="bi bi-arrow-left"></i> العودة إلى المستخدمين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- قسم إدارة الأدوار -->
    <div class="card shadow-sm mt-4">
        <div class="card-body">
            <h4 class="text-primary mb-3">
                <i class="bi bi-person-gear me-2"></i> أدوار المستخدم
            </h4>
            <p class="text-muted">لإدارة أدوار هذا المستخدم، يرجى استخدام صفحة إدارة الأدوار المخصصة.</p>
            <a asp-action="ManageRoles" asp-route-userId="@Model.Id" class="btn btn-primary">
                <i class="bi bi-person-gear"></i> إدارة أدوار المستخدم
            </a>
        </div>
    </div>
</div>