using Core.Entities;

namespace Core.Specifications.WhatsAppSettingsSpec
{
    public class GetSettingByKey : Specification<WhatsAppSettings>
    {
        public GetSettingByKey(string key) : base(s => s.SettingKey == key && s.IsActive)
        {
            includes.Add(s => s.UpdatedByUser);
        }

        public GetSettingByKey(string key, bool includeInactive) : base(s => s.SettingKey == key && (includeInactive || s.IsActive))
        {
            includes.Add(s => s.UpdatedByUser);
        }
    }
}
